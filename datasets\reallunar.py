import os
import numpy as np
from PIL import Image
import torch
from torch.utils import data
from datasets import utils


class RealLunarDataset(data.Dataset):
    """RealLunar Dataset for semantic segmentation

    A real lunar surface dataset containing terrain segmentation data.
    Dataset contains PCAM and TCAM images with grayscale mask annotations.

    Classes (when merge_rocks=False):
        0: Background (lunar surface)
        1: Object class 1 (grayscale value 29)
        2: Object class 2 (grayscale value 76)
        3: Object class 3 (grayscale value 150)

    Classes (when merge_rocks=True):
        0: Background (lunar surface)
        1: Rocks (merged from all object classes)
    """
    
    def __init__(self, root, split='train', transform=None, camera_type='both', merge_rocks=False):
        """
        Args:
            root (str): Root directory of the dataset
            split (str): Dataset split ('train', 'val', 'test')
            transform: Data augmentation transforms
            camera_type (str): Camera type to use ('PCAM', 'TCAM', 'both')
            merge_rocks (bool): If True, merge all object classes into single 'Rocks' class (2 classes total)
                               If False, keep separate object classes (4 classes total)
        """
        self.root = os.path.expanduser(root)
        self.split = split
        self.transform = transform
        self.camera_type = camera_type
        self.merge_rocks = merge_rocks

        # Dataset path
        self.dataset_path = os.path.join(self.root, 'RealLunar')

        # Grayscale value to class mapping
        self.grayscale_to_class = {
            0: 0,    # Background
            29: 1,   # Object class 1
            76: 2,   # Object class 2
            150: 3   # Object class 3
        }

        # Class information - depends on merge_rocks setting
        if merge_rocks:
            self.num_classes = 2  # Background + Rocks (merged)
            self.class_names = ['Background', 'Rocks']
            print("RealLunar Dataset Mode: 2-class (Background + Rocks) - Compatible with Jiayu/LunarSim evaluation")
        else:
            self.num_classes = 4  # Background + 3 object classes
            self.class_names = ['Background', 'Object_Class_1', 'Object_Class_2', 'Object_Class_3']
            print("RealLunar Dataset Mode: 4-class (Background + 3 Object Classes) - Using grayscale masks")

        self.ignore_index = 255

        # Color mapping for visualization (RGB format)
        self.background_color = [0, 0, 0]      # Black for background
        if merge_rocks:
            self.rock_color = [255, 0, 0]      # Red for merged rocks
            print(f"Color Detection: Background={self.background_color}, Rocks={self.rock_color}")
        else:
            self.class1_color = [0, 255, 0]    # Green for class 1
            self.class2_color = [0, 0, 255]    # Blue for class 2
            self.class3_color = [255, 255, 0]  # Yellow for class 3
            print(f"Color Detection: Background={self.background_color}, Class1={self.class1_color}, Class2={self.class2_color}, Class3={self.class3_color}")

        # Get all available samples
        self.samples = self._get_samples()

        # Split dataset: 70% train, 20% val, 10% test
        self.train_samples, self.val_samples, self.test_samples = self._split_dataset()

        # Select samples based on split
        if split == 'train':
            self.current_samples = self.train_samples
        elif split == 'val':
            self.current_samples = self.val_samples
        elif split == 'test':
            self.current_samples = self.test_samples
        else:
            raise ValueError(f"Invalid split: {split}. Must be 'train', 'val', or 'test'")

        print(f"RealLunar Dataset - {split}: {len(self.current_samples)} samples")

    def _get_samples(self):
        """Get all available samples from the dataset directory"""
        samples = []

        # Find all image files and their corresponding grayscale masks
        for filename in os.listdir(self.dataset_path):
            if filename.endswith('.png') and not filename.startswith('g_'):
                # This is an original image file
                camera_type = filename.split('.')[0]  # e.g., 'PCAM1' or 'TCAM1'

                # Check if we should include this camera type
                if self.camera_type == 'PCAM' and not camera_type.startswith('PCAM'):
                    continue
                elif self.camera_type == 'TCAM' and not camera_type.startswith('TCAM'):
                    continue

                # Check if corresponding grayscale mask exists
                mask_filename = f'g_{filename}'
                img_path = os.path.join(self.dataset_path, filename)
                mask_path = os.path.join(self.dataset_path, mask_filename)

                if os.path.exists(mask_path):
                    # Extract camera type and frame id
                    if camera_type.startswith('PCAM'):
                        camera = 'PCAM'
                        frame_id = int(camera_type[4:])  # Remove 'PCAM' prefix
                    elif camera_type.startswith('TCAM'):
                        camera = 'TCAM'
                        frame_id = int(camera_type[4:])  # Remove 'TCAM' prefix
                    else:
                        continue

                    samples.append({
                        'camera': camera,
                        'frame_id': frame_id,
                        'image': img_path,
                        'mask': mask_path
                    })

        # Sort by camera type and frame id
        samples.sort(key=lambda x: (x['camera'], x['frame_id']))
        return samples
    
    def _split_dataset(self):
        """Split dataset into train/val/test with 7:2:1 ratio"""
        total_samples = len(self.samples)
        
        # Calculate split indices
        train_end = int(0.7 * total_samples)
        val_end = int(0.9 * total_samples)
        
        train_samples = self.samples[:train_end]
        val_samples = self.samples[train_end:val_end]
        test_samples = self.samples[val_end:]
        
        print(f"Dataset split - Train: {len(train_samples)}, Val: {len(val_samples)}, Test: {len(test_samples)}")

        return train_samples, val_samples, test_samples
    
    def _convert_grayscale_to_classes(self, grayscale_mask):
        """Convert grayscale mask values to class indices

        Args:
            grayscale_mask: numpy array with grayscale values

        Returns:
            numpy array with class indices
        """
        # Create output mask with same shape
        class_mask = np.zeros_like(grayscale_mask, dtype=np.uint8)

        # Map grayscale values to class indices
        for gray_val, class_id in self.grayscale_to_class.items():
            if self.merge_rocks and class_id > 0:
                # Merge all non-background classes into class 1
                class_mask[grayscale_mask == gray_val] = 1
            else:
                class_mask[grayscale_mask == gray_val] = class_id

        return class_mask
    
    def __getitem__(self, index):
        """Get a sample from the dataset"""
        sample = self.current_samples[index]

        # Load image and convert to RGB
        image = Image.open(sample['image']).convert('RGB')

        # Load grayscale mask and convert to class indices
        grayscale_mask = Image.open(sample['mask']).convert('L')
        grayscale_array = np.array(grayscale_mask)

        # Convert grayscale values to class indices
        class_array = self._convert_grayscale_to_classes(grayscale_array)
        mask = Image.fromarray(class_array)

        # Apply transforms
        if self.transform is not None:
            image, mask = self.transform(image, mask)

        return image, mask
    
    def __len__(self):
        return len(self.current_samples)
    
    def get_class_names(self):
        """Get class names"""
        return self.class_names
    
    def get_class_colors(self):
        """Get class colors for visualization (RGB format)"""
        if self.merge_rocks:
            return [
                self.background_color,  # Background
                self.rock_color,        # Merged rocks
            ]
        else:
            return [
                self.background_color,  # Background
                self.class1_color,      # Object class 1
                self.class2_color,      # Object class 2
                self.class3_color,      # Object class 3
            ]

    def decode_target(self, target):
        """Convert class indices to RGB colors for visualization

        Args:
            target: numpy array with class indices

        Returns:
            numpy array with RGB colors for visualization
        """
        # Define color mapping based on mode
        if self.merge_rocks:
            color_map = np.array([
                self.background_color,  # Background
                self.rock_color,        # Merged rocks
            ], dtype=np.uint8)
        else:
            color_map = np.array([
                self.background_color,  # Background
                self.class1_color,      # Object class 1
                self.class2_color,      # Object class 2
                self.class3_color,      # Object class 3
            ], dtype=np.uint8)

        # Handle invalid class indices
        target = np.clip(target, 0, len(color_map) - 1)

        # Convert to RGB
        rgb_target = color_map[target]

        return rgb_target


if __name__ == '__main__':
    # Test the dataset
    import matplotlib.pyplot as plt

    # Test dataset loading
    dataset_root = './datasets/data'

    print("Testing RealLunar Dataset with grayscale masks...")

    for merge_rocks in [False, True]:
        mode_name = "2-class (merged rocks)" if merge_rocks else "4-class (grayscale)"
        print(f"\n=== Testing {mode_name} mode ===")

        for split in ['train', 'val', 'test']:
            try:
                dataset = RealLunarDataset(root=dataset_root, split=split, merge_rocks=merge_rocks)
                print(f"\n{split.upper()} Dataset ({mode_name}):")
                print(f"  Samples: {len(dataset)}")
                print(f"  Classes: {dataset.num_classes}")
                print(f"  Class names: {dataset.get_class_names()}")

                if len(dataset) > 0:
                    # Test loading first sample
                    image, mask = dataset[0]
                    print(f"  Sample 0 - Image shape: {np.array(image).shape}, Mask shape: {np.array(mask).shape}")
                    print(f"  Mask unique values: {np.unique(np.array(mask))}")

                    # Test visualization
                    decoded_mask = dataset.decode_target(np.array(mask))
                    print(f"  Decoded mask shape: {decoded_mask.shape}")

            except Exception as e:
                print(f"Error loading {split} dataset: {e}")
                import traceback
                traceback.print_exc()

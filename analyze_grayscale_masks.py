#!/usr/bin/env python3
"""
Analyze grayscale mask files in RealLunar dataset to understand their format
"""

import os
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt
from collections import Counter

def analyze_grayscale_masks(dataset_path):
    """Analyze grayscale mask files to understand their format"""
    
    print("Analyzing RealLunar grayscale masks...")
    print("=" * 50)
    
    # Find all grayscale mask files
    mask_files = []
    for filename in os.listdir(dataset_path):
        if filename.startswith('g_') and filename.endswith('.png'):
            mask_files.append(os.path.join(dataset_path, filename))
    
    print(f"Found {len(mask_files)} grayscale mask files")
    
    if not mask_files:
        print("No grayscale mask files found!")
        return
    
    # Analyze first few mask files
    unique_values_all = set()
    
    for i, mask_path in enumerate(mask_files[:10]):  # Analyze first 10 files
        print(f"\nAnalyzing: {os.path.basename(mask_path)}")
        
        # Load mask as grayscale
        mask = Image.open(mask_path).convert('L')
        mask_array = np.array(mask)
        
        print(f"  Shape: {mask_array.shape}")
        print(f"  Data type: {mask_array.dtype}")
        print(f"  Min value: {mask_array.min()}")
        print(f"  Max value: {mask_array.max()}")
        
        # Get unique values
        unique_values = np.unique(mask_array)
        print(f"  Unique values: {unique_values}")
        
        # Count pixels for each value
        value_counts = Counter(mask_array.flatten())
        print(f"  Value distribution:")
        for value, count in sorted(value_counts.items()):
            percentage = (count / mask_array.size) * 100
            print(f"    Value {value}: {count} pixels ({percentage:.2f}%)")
        
        unique_values_all.update(unique_values)
        
        # Save a sample visualization for the first file
        if i == 0:
            plt.figure(figsize=(12, 4))
            
            # Original image
            original_path = mask_path.replace('g_', '')
            if os.path.exists(original_path):
                original = Image.open(original_path)
                plt.subplot(1, 3, 1)
                plt.imshow(original)
                plt.title('Original Image')
                plt.axis('off')
            
            # Grayscale mask
            plt.subplot(1, 3, 2)
            plt.imshow(mask_array, cmap='gray')
            plt.title('Grayscale Mask')
            plt.colorbar()
            plt.axis('off')
            
            # Colored mask (assuming 0=background, other values=classes)
            plt.subplot(1, 3, 3)
            colored_mask = np.zeros((*mask_array.shape, 3), dtype=np.uint8)
            for value in unique_values:
                if value == 0:
                    continue  # Keep background black
                elif value == 1:
                    colored_mask[mask_array == value] = [0, 255, 0]  # Green
                elif value == 2:
                    colored_mask[mask_array == value] = [0, 0, 255]  # Blue
                else:
                    # Use a random color for other values
                    color = np.random.randint(0, 255, 3)
                    colored_mask[mask_array == value] = color
            
            plt.imshow(colored_mask)
            plt.title('Colored Interpretation')
            plt.axis('off')
            
            plt.tight_layout()
            plt.savefig('reallunar_mask_analysis.png', dpi=150, bbox_inches='tight')
            print(f"  Saved visualization to: reallunar_mask_analysis.png")
    
    print(f"\nOverall Analysis:")
    print(f"All unique values found across samples: {sorted(unique_values_all)}")
    
    # Suggest class mapping
    print(f"\nSuggested class mapping:")
    sorted_values = sorted(unique_values_all)
    for i, value in enumerate(sorted_values):
        if value == 0:
            print(f"  Value {value} -> Class 0 (Background)")
        else:
            print(f"  Value {value} -> Class {i} (Object class {i})")

if __name__ == '__main__':
    dataset_path = './datasets/data/RealLunar'
    
    if not os.path.exists(dataset_path):
        print(f"Dataset path not found: {dataset_path}")
        print("Please make sure the RealLunar dataset is in the correct location.")
    else:
        analyze_grayscale_masks(dataset_path)
